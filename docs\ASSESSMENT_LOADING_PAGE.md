# Assessment Loading Page

Halaman loading yang interaktif dan informatif untuk menampilkan progress assessment kepada user saat menunggu hasil analisis AI.

## Fitur Utama

### 🎨 Visual yang Menarik
- **Animasi Lottie**: Menggunakan animasi buku yang bergerak untuk memberikan feedback visual
- **Gradient Background**: Background yang menarik dengan gradasi warna
- **Progress Bar**: Menampilkan progress real-time dari proses assessment
- **Status Cards**: Kartu status yang informatif dengan ikon dan warna yang sesuai

### 📊 Informasi Real-time
- **Status Workflow**: Menampilkan tahapan proses (validating, submitting, queued, processing, completed)
- **Progress Percentage**: Persentase progress yang akurat
- **Queue Position**: Posisi dalam antrian jika menggunakan real API
- **Estimated Time**: Estimasi waktu yang tersisa
- **Elapsed Time**: Waktu yang telah berlalu

### 🔄 Tahapan Proses
1. **Validating** - Memvalidasi jawaban assessment
2. **Submitting** - Mengirim data ke sistem AI
3. **Queued** - Menunggu giliran dalam antrian
4. **Processing** - AI menganalisis data
5. **Generating** - Membuat profil talenta

### ⚡ Interaktivitas
- **Cancel Button**: Tombol untuk membatalkan proses
- **Retry Button**: Tombol untuk mencoba lagi jika gagal
- **Auto Redirect**: Otomatis redirect ke hasil setelah selesai

## Cara Penggunaan

### 1. Menggunakan Hook `useAssessmentSubmission`

```typescript
import { useAssessmentSubmission } from '../hooks/useAssessmentSubmission';

function MyComponent() {
  const { submitAssessment } = useAssessmentSubmission({
    assessmentName: 'AI-Driven Talent Mapping',
    onSubmissionStart: () => console.log('Starting submission...'),
    onSubmissionError: (error) => console.error('Submission failed:', error)
  });

  const handleSubmit = async () => {
    const success = await submitAssessment(answers);
    if (success) {
      // User akan diarahkan ke halaman loading
    }
  };

  return (
    <button onClick={handleSubmit}>
      Submit Assessment
    </button>
  );
}
```

### 2. Menggunakan Komponen Langsung

```typescript
import AssessmentLoadingPage from '../components/assessment/AssessmentLoadingPage';

function MyLoadingPage() {
  const workflowState = {
    status: 'processing',
    progress: 75,
    message: 'AI sedang menganalisis data...',
    queuePosition: 2,
    estimatedTimeRemaining: '1 menit'
  };

  return (
    <AssessmentLoadingPage
      workflowState={workflowState}
      onCancel={() => console.log('Cancelled')}
      onRetry={() => console.log('Retrying')}
    />
  );
}
```

### 3. Integrasi dengan Enhanced Assessment Submission

```typescript
<EnhancedAssessmentSubmission
  answers={answers}
  onComplete={handleComplete}
  onCancel={handleCancel}
  showLoadingPageOption={true} // Menampilkan opsi loading page
/>
```

## Routing

### URL Endpoints
- `/assessment-loading` - Halaman loading utama
- `/assessment-loading-demo` - Demo halaman loading

### Parameter URL (Opsional)
- `answers` - Data jawaban assessment (JSON encoded)
- `name` - Nama assessment

### Local Storage
Halaman loading menggunakan localStorage untuk menyimpan:
- `assessment-answers` - Jawaban assessment
- `assessment-name` - Nama assessment
- `assessment-submission-time` - Waktu submission

## Konfigurasi

### Props AssessmentLoadingPage

```typescript
interface AssessmentLoadingPageProps {
  workflowState: WorkflowState;     // Status dan progress workflow
  onCancel?: () => void;            // Callback saat cancel
  onRetry?: () => void;             // Callback saat retry
  className?: string;               // CSS class tambahan
}
```

### WorkflowState

```typescript
interface WorkflowState {
  status: WorkflowStatus;           // Status saat ini
  progress?: number;                // Progress 0-100
  message?: string;                 // Pesan status
  queuePosition?: number;           // Posisi dalam antrian
  estimatedTimeRemaining?: string;  // Estimasi waktu tersisa
  jobId?: string;                   // ID job untuk tracking
  result?: AssessmentResult;        // Hasil assessment
}
```

## Demo

Untuk melihat demo halaman loading:

1. Buka `/assessment-loading-demo`
2. Gunakan kontrol demo untuk mengubah status
3. Klik "Auto Play" untuk melihat transisi otomatis
4. Klik "Reset" untuk kembali ke awal

## Customization

### Mengubah Animasi Lottie
1. Ganti file `/public/loading/loading.json` dengan animasi baru
2. Pastikan format file sesuai dengan Lottie Web

### Mengubah Tahapan Proses
Edit array `LOADING_STEPS` di `AssessmentLoadingPage.tsx`:

```typescript
const LOADING_STEPS: LoadingStep[] = [
  {
    id: 'custom-step',
    title: 'Custom Step',
    description: 'Deskripsi step custom',
    icon: CustomIcon,
    estimatedTime: 10
  }
];
```

### Mengubah Styling
Komponen menggunakan Tailwind CSS. Ubah class CSS sesuai kebutuhan:

```typescript
<div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
  {/* Konten */}
</div>
```

## Troubleshooting

### Animasi Lottie Tidak Muncul
1. Pastikan file `/public/loading/loading.json` ada
2. Check console untuk error loading Lottie
3. Pastikan `lottie-web` terinstall

### Data Assessment Hilang
1. Check localStorage untuk `assessment-answers`
2. Pastikan data disimpan sebelum redirect
3. Gunakan URL parameters sebagai fallback

### Progress Tidak Update
1. Pastikan `workflowState` di-update dengan benar
2. Check callback `onProgress` di workflow
3. Verify API response format

## Dependencies

- `lottie-web` - Untuk animasi Lottie
- `lucide-react` - Untuk ikon
- `@radix-ui/react-*` - Untuk komponen UI
- `tailwindcss` - Untuk styling

## File Structure

```
components/assessment/
├── AssessmentLoadingPage.tsx     # Komponen utama loading page
├── EnhancedAssessmentSubmission.tsx  # Integrasi dengan submission
└── AssessmentSidebar.tsx         # Submit button di sidebar

hooks/
└── useAssessmentSubmission.ts    # Hook untuk submission

app/
├── assessment-loading/
│   └── page.tsx                  # Route halaman loading
└── assessment-loading-demo/
    └── page.tsx                  # Route demo

public/loading/
└── loading.json                  # Animasi Lottie
```

## Best Practices

1. **Selalu validasi data** sebelum redirect ke loading page
2. **Gunakan localStorage** untuk persistence data
3. **Berikan feedback visual** yang jelas untuk setiap status
4. **Handle error cases** dengan baik
5. **Provide cancel option** untuk user experience yang baik
6. **Auto redirect** setelah completion untuk smooth flow
